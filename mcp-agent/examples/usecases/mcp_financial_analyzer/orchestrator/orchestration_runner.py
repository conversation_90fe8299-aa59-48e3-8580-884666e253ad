"""
Orchestration Runner for Financial Analysis Workflows
===================================================

Main execution engine that coordinates the financial orchestrator, context management,
query processing, and workflow patterns to provide a unified interface for complex
financial analysis tasks.

This module serves as the primary integration point between all orchestration
components and provides the main API for external systems.
"""

import asyncio
import uuid
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from orchestrator.financial_orchestrator import FinancialOrchestrator, FinancialAnalysisContext
from orchestrator.context_manager import ContextManager, FinancialWorkflowContext
from orchestrator.query_processor import FinancialQueryProcessor, QueryType, WorkflowPattern
from orchestrator.workflow_patterns import WorkflowPatternRegistry, WorkflowExecutor

from mcp_agent.logging.logger import get_logger

logger = get_logger(__name__)


class OrchestrationRunner:
    """
    Main orchestration runner that coordinates all financial analysis workflows.
    
    This class provides the primary interface for executing financial analysis
    queries using the coordinated agent system.
    """
    
    def __init__(
        self,
        mysql_agent: Any,
        shortage_agent: Any,
        alert_agent: Any,
        llm_factory: Any,
        context_dir: Optional[str] = None,
        persist_context: bool = True
    ):
        """
        Initialize the orchestration runner.
        
        Args:
            mysql_agent: MySQL analysis agent instance
            shortage_agent: Shortage analyzer agent instance  
            alert_agent: Alert manager agent instance
            llm_factory: Factory for creating LLM instances
            context_dir: Directory for persisting workflow contexts
            persist_context: Whether to persist contexts to disk
        """
        self.mysql_agent = mysql_agent
        self.shortage_agent = shortage_agent
        self.alert_agent = alert_agent
        self.llm_factory = llm_factory
        
        # Initialize orchestration components
        self.context_manager = ContextManager(
            persist_context=persist_context,
            context_dir=context_dir
        )
        
        self.query_processor = FinancialQueryProcessor()
        self.pattern_registry = WorkflowPatternRegistry()
        self.workflow_executor = WorkflowExecutor(self.pattern_registry)
        
        # Create the main financial orchestrator
        self.orchestrator = FinancialOrchestrator(
            llm_factory=llm_factory,
            mysql_agent=mysql_agent,
            shortage_agent=shortage_agent,
            alert_agent=alert_agent,
            name="MainFinancialOrchestrator"
        )
        
        # Execution statistics
        self.execution_stats = {
            "total_workflows": 0,
            "successful_workflows": 0,
            "failed_workflows": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0
        }
        
        logger.info("OrchestrationRunner initialized with all components")
    
    async def execute_financial_query(
        self,
        query: str,
        workflow_id: Optional[str] = None,
        execution_mode: str = "pattern_based",  # "pattern_based" or "orchestrator_based"
        **kwargs
    ) -> Dict[str, Any]:
        """
        Execute a financial analysis query using the orchestrated agent system.
        
        Args:
            query: Natural language financial analysis query
            workflow_id: Optional workflow ID (generated if not provided)
            execution_mode: Execution mode ("pattern_based" or "orchestrator_based")
            **kwargs: Additional parameters
            
        Returns:
            Comprehensive analysis results dictionary
        """
        if not workflow_id:
            workflow_id = f"workflow_{uuid.uuid4().hex[:8]}"
        
        start_time = datetime.now()
        logger.info(f"Starting financial query execution: {workflow_id}")
        logger.info(f"Query: {query}")
        
        try:
            # Update execution statistics
            self.execution_stats["total_workflows"] += 1
            
            # Step 1: Parse and analyze the query
            parsed_query = self.query_processor.process_query(query)
            logger.info(f"Query parsed as {parsed_query.query_type.value} "
                       f"(confidence: {parsed_query.confidence:.2f})")
            
            # Check if query needs clarification
            if not self.query_processor.is_query_clear(parsed_query):
                logger.warning("Query requires clarification")
                return {
                    "success": False,
                    "workflow_id": workflow_id,
                    "requires_clarification": True,
                    "ambiguity_flags": parsed_query.ambiguity_flags,
                    "suggested_clarifications": parsed_query.suggested_clarifications,
                    "parsed_query": {
                        "query_type": parsed_query.query_type.value,
                        "confidence": parsed_query.confidence,
                        "entities": parsed_query.entities.get_all_entities()
                    }
                }
            
            # Step 2: Create workflow context
            context = self.context_manager.create_context(
                workflow_id=workflow_id,
                query=query,
                query_type=parsed_query.query_type.value,
                workflow_pattern=parsed_query.workflow_pattern.value
            )
            
            # Step 3: Execute based on selected mode
            if execution_mode == "pattern_based":
                results = await self._execute_pattern_based(
                    workflow_id, parsed_query, context, **kwargs
                )
            else:  # orchestrator_based
                results = await self._execute_orchestrator_based(
                    workflow_id, parsed_query, context, **kwargs
                )
            
            # Step 4: Finalize results
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # Update statistics
            if results.get("success", False):
                self.execution_stats["successful_workflows"] += 1
            else:
                self.execution_stats["failed_workflows"] += 1
                
            self.execution_stats["total_execution_time"] += execution_time
            self.execution_stats["average_execution_time"] = (
                self.execution_stats["total_execution_time"] / 
                self.execution_stats["total_workflows"]
            )
            
            # Add execution metadata
            results.update({
                "workflow_id": workflow_id,
                "execution_time": execution_time,
                "execution_mode": execution_mode,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "query_analysis": {
                    "type": parsed_query.query_type.value,
                    "confidence": parsed_query.confidence,
                    "complexity": parsed_query.complexity_score,
                    "entities_found": parsed_query.entities.has_entities(),
                    "workflow_pattern": parsed_query.workflow_pattern.value
                }
            })
            
            # Get final context summary
            context_summary = self.context_manager.get_full_context_summary(workflow_id)
            results["context_summary"] = context_summary
            
            # Cleanup context
            self.context_manager.cleanup_context(workflow_id)
            
            logger.info(f"Financial query execution completed: {workflow_id} "
                       f"(success: {results.get('success', False)}, "
                       f"time: {execution_time:.2f}s)")
            
            return results
            
        except Exception as e:
            logger.error(f"Financial query execution failed: {e}")
            self.execution_stats["failed_workflows"] += 1
            
            return {
                "success": False,
                "workflow_id": workflow_id,
                "error": str(e),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "execution_mode": execution_mode
            }
    
    async def _execute_pattern_based(
        self,
        workflow_id: str,
        parsed_query: Any,
        context: FinancialWorkflowContext,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute using predefined workflow patterns."""
        logger.info(f"Executing pattern-based workflow for {workflow_id}")
        
        # Get appropriate workflow pattern
        # Prefer the processor's computed workflow_pattern when it implies a specific agent sequence
        selected_pattern_id = None
        computed_pattern = getattr(parsed_query.workflow_pattern, "value", None)
        if computed_pattern in {"full_workflow", "mysql_shortage"}:
            # MySQL → Shortage → (Alert optional) maps to our 'shortage_analysis' pattern
            selected_pattern_id = "shortage_analysis"
        elif computed_pattern == "mysql_alert":
            # MySQL → Alert maps to 'customer_priority' pattern shape
            selected_pattern_id = "customer_priority"
        elif computed_pattern == "alert_only":
            # Fallback to customer_priority for alert-centric flow
            selected_pattern_id = "customer_priority"
        elif computed_pattern == "mysql_only":
            # No dedicated MySQL-only pattern. Use customer_priority (MySQL first) and allow alert step to be optional/no-op
            selected_pattern_id = "customer_priority"

        if selected_pattern_id:
            pattern = self.pattern_registry.get_pattern(selected_pattern_id)
        else:
            # Fallback: derive by query type mapping
            pattern = self.pattern_registry.get_pattern_for_query_type(parsed_query.query_type.value)

        if not pattern:
            from .exceptions import OrchestrationError
            raise OrchestrationError(
                message=f"No pattern found for computed/query type (computed={computed_pattern}, query_type={parsed_query.query_type.value})",
                workflow_id=workflow_id,
                error_code="NO_PATTERN_FOR_QUERY_TYPE",
            )
        
        # Prepare agents dictionary
        agents = {
            "mysql_analyzer": self.mysql_agent,
            "shortage_analyzer": self.shortage_agent,
            "alert_manager": self.alert_agent
        }
        
        # Prepare input data
        input_data = {
            "original_query": parsed_query.original_query,
            "entities": parsed_query.entities.get_all_entities(),
            "parameters": {
                "quantities": parsed_query.parameters.quantities,
                "dates": parsed_query.parameters.dates,
                "priorities": parsed_query.parameters.priorities
            }
        }
        
        # Execute workflow pattern
        results = await self.workflow_executor.execute_pattern(
            pattern_id=pattern.pattern_id,
            workflow_id=workflow_id,
            agents=agents,
            context_manager=self.context_manager,
            input_data=input_data
        )
        
        return results
    
    async def _execute_orchestrator_based(
        self,
        workflow_id: str,
        parsed_query: Any,
        context: FinancialWorkflowContext,
        **kwargs
    ) -> Dict[str, Any]:
        """Execute using the financial orchestrator."""
        logger.info(f"Executing orchestrator-based workflow for {workflow_id}")
        
        # Use the financial orchestrator's analyze_financial_query method
        results = await self.orchestrator.analyze_financial_query(
            query=parsed_query.original_query
        )
        
        # Add workflow metadata
        results["workflow_id"] = workflow_id
        results["execution_method"] = "orchestrator_based"
        
        return results
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics for monitoring and optimization."""
        return self.execution_stats.copy()
    
    def get_supported_query_types(self) -> List[str]:
        """Get list of supported query types."""
        return [qt.value for qt in QueryType]
    
    def get_available_patterns(self) -> Dict[str, str]:
        """Get available workflow patterns."""
        patterns = self.pattern_registry.get_all_patterns()
        return {pid: pattern.name for pid, pattern in patterns.items()}
    
    async def validate_agent_connectivity(self) -> Dict[str, bool]:
        """Validate that all agents are properly connected and functional.
        Returns keys aligned with API spec: mysql_analyzer, shortage_analyzer, alert_manager."""
        connectivity: Dict[str, bool] = {}

        # Test MySQL analyzer agent
        try:
            if hasattr(self.mysql_agent, 'initialize_llm'):
                await self.mysql_agent.initialize_llm()
            connectivity["mysql_analyzer"] = True
        except Exception as e:
            logger.error(f"MySQL agent connectivity test failed: {e}")
            connectivity["mysql_analyzer"] = False

        # Test shortage analyzer agent
        try:
            if hasattr(self.shortage_agent, 'initialize_llm'):
                await self.shortage_agent.initialize_llm()
            connectivity["shortage_analyzer"] = True
        except Exception as e:
            logger.error(f"Shortage agent connectivity test failed: {e}")
            connectivity["shortage_analyzer"] = False

        # Test alert manager agent
        try:
            if hasattr(self.alert_agent, 'initialize_llm'):
                await self.alert_agent.initialize_llm()
            connectivity["alert_manager"] = True
        except Exception as e:
            logger.error(f"Alert agent connectivity test failed: {e}")
            connectivity["alert_manager"] = False

        return connectivity
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check of the orchestration system."""
        health = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "components": {},
            "statistics": self.execution_stats
        }
        
        # Check component health
        try:
            # Context manager
            health["components"]["context_manager"] = {
                "status": "healthy",
                "active_contexts": len(self.context_manager.active_contexts),
                "persist_enabled": self.context_manager.persist_context
            }
            
            # Query processor
            health["components"]["query_processor"] = {
                "status": "healthy",
                "supported_types": len(self.query_processor.keywords),
                "patterns_loaded": len(self.query_processor.patterns)
            }
            
            # Pattern registry
            health["components"]["pattern_registry"] = {
                "status": "healthy",
                "patterns_count": len(self.pattern_registry.patterns)
            }
            
            # Agent connectivity (keys aligned to API spec)
            connectivity = await self.validate_agent_connectivity()
            health["components"]["agents"] = connectivity

            # Overall health determination
            all_agents_healthy = all(connectivity.values())
            if not all_agents_healthy:
                health["status"] = "degraded"
                health["issues"] = [
                    f"Agent {agent} is not healthy"
                    for agent, status in connectivity.items()
                    if not status
                ]

        except Exception as e:
            health["status"] = "unhealthy"
            health["error"] = str(e)
        
        return health
    
    def reset_statistics(self) -> None:
        """Reset execution statistics."""
        self.execution_stats = {
            "total_workflows": 0,
            "successful_workflows": 0,
            "failed_workflows": 0,
            "total_execution_time": 0.0,
            "average_execution_time": 0.0
        }
        logger.info("Execution statistics reset")


def create_orchestration_runner(
    mysql_agent: Any,
    shortage_agent: Any,
    alert_agent: Any,
    llm_factory: Any,
    **kwargs
) -> OrchestrationRunner:
    """
    Factory function to create a configured orchestration runner.
    
    Args:
        mysql_agent: MySQL analysis agent
        shortage_agent: Shortage analyzer agent
        alert_agent: Alert manager agent  
        llm_factory: LLM factory function
        **kwargs: Additional configuration options
        
    Returns:
        Configured OrchestrationRunner instance
    """
    runner = OrchestrationRunner(
        mysql_agent=mysql_agent,
        shortage_agent=shortage_agent,
        alert_agent=alert_agent,
        llm_factory=llm_factory,
        **kwargs
    )
    
    logger.info("OrchestrationRunner created via factory function")
    return runner
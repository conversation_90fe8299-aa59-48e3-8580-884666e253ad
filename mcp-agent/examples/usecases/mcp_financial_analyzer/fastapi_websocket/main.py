import json
import logging
import logging.config
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.responses import HTMLResponse
import uvicorn
from contextlib import asynccontextmanager

from session_manager import FinancialSessionManager, SessionType, setup_comprehensive_logging

def is_websocket_connected(websocket: WebSocket) -> bool:
    """
    Check if WebSocket is connected and ready for communication.

    Args:
        websocket: The WebSocket instance to check

    Returns:
        bool: True if connected, False otherwise
    """
    try:
        # Primary check: client state should be CONNECTED
        has_client_state = hasattr(websocket, 'client_state')
        if not has_client_state:
            return False

        client_state_connected = websocket.client_state.name == "CONNECTED"

        # For streaming scenarios, we primarily rely on client state
        # Application state can be unreliable during long-running operations
        return client_state_connected

    except (AttributeError, Exception):
        return False

def log_websocket_state(websocket: WebSocket, connection_id: str, context: str = "") -> None:
    """
    Log current WebSocket connection state with detailed information.
    
    Args:
        websocket: The WebSocket instance
        connection_id: Identifier for the connection
        context: Additional context about when this check is happening
    """
    try:
        client_state = getattr(websocket, 'client_state', None)
        app_state = getattr(websocket, 'application_state', None)
        
        client_state_name = client_state.name if client_state else "NO_CLIENT_STATE"
        app_state_name = app_state.name if app_state else "NO_APP_STATE"
        
        is_connected = is_websocket_connected(websocket)
        
        # Use INFO level so it actually appears in logs (debug wasn't showing)
        logger.info(f"WebSocket state for {connection_id} {context}: client={client_state_name}, app={app_state_name}, connected={is_connected}")
    except Exception as e:
        logger.info(f"WebSocket state for {connection_id} {context}: ERROR checking state - {e}")

# Configure detailed debug logging
logging_config = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s() - %(message)s'
        },
        'simple': {
            'format': '%(asctime)s - %(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'debug_file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'logs/main_debug.log',
            'formatter': 'detailed',
            'mode': 'a'
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        }
    },
    'loggers': {
        '': {  # root logger
            'handlers': ['debug_file', 'console'],
            'level': 'DEBUG',
            'propagate': False
        },
        'uvicorn': {
            'handlers': ['debug_file'],
            'level': 'DEBUG',
            'propagate': False
        },
        'fastapi': {
            'handlers': ['debug_file'],
            'level': 'DEBUG',
            'propagate': False
        }
    }
}

logging.config.dictConfig(logging_config)
logger = logging.getLogger(__name__)

# Log startup
logger.info("="*60)
logger.info("MCP Financial Analyzer WebSocket Server Starting")
logger.info(f"Startup time: {datetime.now().isoformat()}")
logger.info("="*60)

# Global session manager
session_manager = FinancialSessionManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup and shutdown events for the FastAPI application."""
    # Startup
    logger.info("FastAPI lifespan: Starting up application with comprehensive orchestration")

    # Initialize comprehensive logging
    try:
        debug_log_file, llm_debug_file = setup_comprehensive_logging(show_llm_realtime=False)
        logger.info(f"Comprehensive logging initialized - Debug: {debug_log_file}, LLM: {llm_debug_file}")
    except Exception as e:
        logger.warning(f"Failed to setup comprehensive logging: {e}")

    logger.debug("Initializing session manager with orchestration...")
    try:
        await session_manager.initialize()
        logger.info("Session manager with orchestration initialized successfully")

        # Run initial health check
        if session_manager.orchestration_runner:
            health = await session_manager.run_orchestration_health_check()
            logger.info(f"Initial orchestration health check: {health.get('status', 'unknown')}")
        else:
            logger.warning("Orchestration runner not available - some features will be limited")

    except Exception as e:
        logger.error(f"Failed to initialize session manager: {e}", exc_info=True)
        raise

    yield

    # Shutdown
    logger.info("FastAPI lifespan: Shutting down application")
    logger.debug("Cleaning up session manager...")
    try:
        await session_manager.cleanup()
        logger.info("Session manager cleanup completed")
    except Exception as e:
        logger.error(f"Error during session manager cleanup: {e}", exc_info=True)


app = FastAPI(title="MCP Financial Analyzer WebSocket Server", lifespan=lifespan)


@app.get("/")
async def get():
    """Serve a comprehensive HTML page for testing financial analysis WebSocket connections."""
    logger.debug("Serving HTML test page for WebSocket interface")
    return HTMLResponse("""
<!DOCTYPE html>
<html>
<head>
    <title>MCP Financial Analyzer WebSocket Interface</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .endpoint-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .controls { display: flex; gap: 10px; align-items: center; margin-bottom: 15px; flex-wrap: wrap; }
        .controls input, .controls button { padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; }
        .controls button { background: #007bff; color: white; cursor: pointer; }
        .controls button:hover { background: #0056b3; }
        .controls button:disabled { background: #ccc; cursor: not-allowed; }
        .status { padding: 5px 10px; border-radius: 3px; font-weight: bold; }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .messages { border: 1px solid #ccc; height: 300px; overflow-y: auto; padding: 10px; margin: 10px 0; background: #fafafa; }
        .message { margin: 5px 0; padding: 8px; border-radius: 4px; }
        .message.user { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .message.system { background: #f3e5f5; border-left: 4px solid #9c27b0; }
        .message.result { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .message.progress { background: #fff3e0; border-left: 4px solid #ff9800; }
        .message.error { background: #ffebee; border-left: 4px solid #f44336; }
        .timestamp { font-size: 0.8em; color: #666; }
        .company-input { min-width: 200px; }
        .user-id-input { min-width: 150px; }

        /* Streaming message styles */
        .message.streaming {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            position: relative;
        }
        .stream-content {
            white-space: pre-wrap;
            font-family: monospace;
            margin-bottom: 5px;
        }
        .stream-indicator {
            font-size: 0.9em;
            color: #ff9800;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .streaming .stream-indicator::after {
            content: '';
            width: 12px;
            height: 12px;
            border: 2px solid #ff9800;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 MCP Financial Analyzer WebSocket Interface</h1>
            <p>Test the financial analysis WebSocket endpoints with real-time communication</p>
        </div>

        <!-- Research Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">📊 Financial Research</div>
            <div class="controls">
                <input type="text" id="research-user-id" placeholder="User ID" class="user-id-input" value="user123">
                <input type="text" id="research-company" placeholder="Company Name" class="company-input" value="Apple Inc.">
                <button onclick="connectResearch()">Connect</button>
                <button onclick="disconnectResearch()" disabled id="research-disconnect">Disconnect</button>
                <span id="research-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="research-message" placeholder="Enter your financial research query..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendResearchMessage()" disabled id="research-send">Send</button>
            <div id="research-messages" class="messages"></div>
        </div>

        <!-- Analysis Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">🔍 Financial Analysis</div>
            <div class="controls">
                <input type="text" id="analyze-user-id" placeholder="User ID" class="user-id-input" value="user456">
                <input type="text" id="analyze-company" placeholder="Company Name" class="company-input" value="Tesla Inc.">
                <button onclick="connectAnalyze()">Connect</button>
                <button onclick="disconnectAnalyze()" disabled id="analyze-disconnect">Disconnect</button>
                <span id="analyze-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="analyze-message" placeholder="Enter your analysis request..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendAnalyzeMessage()" disabled id="analyze-send">Send</button>
            <div id="analyze-messages" class="messages"></div>
        </div>

        <!-- Report Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">📈 Financial Report</div>
            <div class="controls">
                <input type="text" id="report-user-id" placeholder="User ID" class="user-id-input" value="user789">
                <input type="text" id="report-company" placeholder="Company Name" class="company-input" value="Microsoft Corp.">
                <button onclick="connectReport()">Connect</button>
                <button onclick="disconnectReport()" disabled id="report-disconnect">Disconnect</button>
                <span id="report-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="report-message" placeholder="Enter your report request..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendReportMessage()" disabled id="report-send">Send</button>
            <div id="report-messages" class="messages"></div>
        </div>

        <!-- Full Analysis Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">🎯 Full Analysis</div>
            <div class="controls">
                <input type="text" id="full-user-id" placeholder="User ID" class="user-id-input" value="user999">
                <input type="text" id="full-company" placeholder="Company Name" class="company-input" value="Amazon.com Inc.">
                <button onclick="connectFull()">Connect</button>
                <button onclick="disconnectFull()" disabled id="full-disconnect">Disconnect</button>
                <span id="full-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="full-message" placeholder="Enter your full analysis request..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendFullMessage()" disabled id="full-send">Send</button>
            <div id="full-messages" class="messages"></div>
        </div>

        <!-- Orchestration Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">🎼 Advanced Orchestration</div>
            <div class="controls">
                <input type="text" id="orchestration-user-id" placeholder="User ID" class="user-id-input" value="user111">
                <input type="text" id="orchestration-company" placeholder="Company Name" class="company-input" value="NVIDIA Corp.">
                <select id="orchestration-mode" style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px;">
                    <option value="orchestrator_based">Orchestrator Based</option>
                    <option value="pattern_based">Pattern Based</option>
                </select>
                <button onclick="connectOrchestration()">Connect</button>
                <button onclick="disconnectOrchestration()" disabled id="orchestration-disconnect">Disconnect</button>
                <span id="orchestration-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="orchestration-message" placeholder="Enter your complex financial analysis query..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendOrchestrationMessage()" disabled id="orchestration-send">Send</button>
            <div id="orchestration-messages" class="messages"></div>
        </div>

        <!-- Demo Scenarios Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">🎭 Demo Scenarios</div>
            <div class="controls">
                <input type="text" id="demo-user-id" placeholder="User ID" class="user-id-input" value="user222">
                <select id="demo-scenario" style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px;">
                    <option value="1">Scenario 1: Critical Database Shortage Analysis</option>
                    <option value="2">Scenario 2: Supplier Risk Alert with SLA Breach</option>
                    <option value="3">Scenario 3: Urgent Customer Priority Alert</option>
                    <option value="4">Scenario 4: Multi-Order Critical Alert</option>
                </select>
                <button onclick="connectDemo()">Connect</button>
                <button onclick="disconnectDemo()" disabled id="demo-disconnect">Disconnect</button>
                <span id="demo-status" class="status disconnected">Disconnected</span>
            </div>
            <button onclick="runDemoScenario()" disabled id="demo-run" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">Run Demo Scenario</button>
            <div id="demo-messages" class="messages"></div>
        </div>
    </div>

    <script>
        let researchWs = null;
        let analyzeWs = null;
        let reportWs = null;
        let fullWs = null;
        let orchestrationWs = null;
        let demoWs = null;

        function addMessage(containerId, type, message, timestamp = new Date()) {
            const container = document.getElementById(containerId);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <div>${message}</div>
                <div class="timestamp">${timestamp.toLocaleTimeString()}</div>
            `;
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        function updateStatus(statusId, connected) {
            const status = document.getElementById(statusId);
            status.textContent = connected ? 'Connected' : 'Disconnected';
            status.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }

        function updateButtons(prefix, connected) {
            document.querySelector(`button[onclick="connect${prefix}()"]`).disabled = connected;
            document.getElementById(`${prefix.toLowerCase()}-disconnect`).disabled = !connected;
            document.getElementById(`${prefix.toLowerCase()}-send`).disabled = !connected;
        }

        // Streaming message handling
        let streamingMessages = {}; // Track streaming messages by container ID

        function handleStreamingMessage(containerId, data) {
            switch(data.type) {
                case 'stream_start':
                    // Start a new streaming message
                    const streamDiv = document.createElement('div');
                    streamDiv.className = 'message assistant streaming';
                    streamDiv.innerHTML = `
                        <div class="stream-content"></div>
                        <div class="stream-indicator">🔄 Streaming...</div>
                        <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                    `;

                    const container = document.getElementById(containerId);
                    container.appendChild(streamDiv);
                    container.scrollTop = container.scrollHeight;

                    // Store reference for streaming updates
                    streamingMessages[containerId] = {
                        element: streamDiv,
                        content: streamDiv.querySelector('.stream-content'),
                        indicator: streamDiv.querySelector('.stream-indicator'),
                        fullText: ''
                    };

                    addMessage(containerId, 'system', data.message);
                    break;

                case 'stream_chunk':
                    // Append chunk to streaming message
                    if (streamingMessages[containerId]) {
                        const streaming = streamingMessages[containerId];
                        streaming.fullText += data.message;
                        streaming.content.textContent = streaming.fullText;

                        // Auto-scroll to bottom
                        const container = document.getElementById(containerId);
                        container.scrollTop = container.scrollHeight;
                    }
                    break;

                case 'stream_end':
                    // Finalize streaming message
                    if (streamingMessages[containerId]) {
                        const streaming = streamingMessages[containerId];
                        streaming.indicator.textContent = '✅ Complete';
                        streaming.indicator.style.color = '#4caf50';
                        streaming.element.classList.remove('streaming');

                        // Clean up reference
                        delete streamingMessages[containerId];
                    }

                    addMessage(containerId, 'system', data.message);
                    break;

                default:
                    // Handle non-streaming messages normally
                    addMessage(containerId, data.type, data.message);
                    break;
            }
        }

        // Research WebSocket
        function connectResearch() {
            const userId = document.getElementById('research-user-id').value;
            const company = document.getElementById('research-company').value;
            researchWs = new WebSocket(`ws://localhost:8000/ws/research/${userId}?company=${encodeURIComponent(company)}`);
            
            researchWs.onopen = function() {
                addMessage('research-messages', 'system', 'Connected to Research endpoint');
                updateStatus('research-status', true);
                updateButtons('Research', true);
            };
            
            researchWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('research-messages', data);
            };
            
            researchWs.onclose = function() {
                addMessage('research-messages', 'system', 'Disconnected from Research endpoint');
                updateStatus('research-status', false);
                updateButtons('Research', false);
            };
        }

        function disconnectResearch() {
            if (researchWs) {
                researchWs.close();
            }
        }

        function sendResearchMessage() {
            const message = document.getElementById('research-message').value;
            if (researchWs && message) {
                researchWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('research-messages', 'user', message);
                document.getElementById('research-message').value = '';
            }
        }

        // Analysis WebSocket
        function connectAnalyze() {
            const userId = document.getElementById('analyze-user-id').value;
            const company = document.getElementById('analyze-company').value;
            analyzeWs = new WebSocket(`ws://localhost:8000/ws/analyze/${userId}?company=${encodeURIComponent(company)}`);
            
            analyzeWs.onopen = function() {
                addMessage('analyze-messages', 'system', 'Connected to Analysis endpoint');
                updateStatus('analyze-status', true);
                updateButtons('Analyze', true);
            };
            
            analyzeWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('analyze-messages', data);
            };
            
            analyzeWs.onclose = function() {
                addMessage('analyze-messages', 'system', 'Disconnected from Analysis endpoint');
                updateStatus('analyze-status', false);
                updateButtons('Analyze', false);
            };
        }

        function disconnectAnalyze() {
            if (analyzeWs) {
                analyzeWs.close();
            }
        }

        function sendAnalyzeMessage() {
            const message = document.getElementById('analyze-message').value;
            if (analyzeWs && message) {
                analyzeWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('analyze-messages', 'user', message);
                document.getElementById('analyze-message').value = '';
            }
        }

        // Report WebSocket
        function connectReport() {
            const userId = document.getElementById('report-user-id').value;
            const company = document.getElementById('report-company').value;
            reportWs = new WebSocket(`ws://localhost:8000/ws/report/${userId}?company=${encodeURIComponent(company)}`);
            
            reportWs.onopen = function() {
                addMessage('report-messages', 'system', 'Connected to Report endpoint');
                updateStatus('report-status', true);
                updateButtons('Report', true);
            };
            
            reportWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('report-messages', data);
            };
            
            reportWs.onclose = function() {
                addMessage('report-messages', 'system', 'Disconnected from Report endpoint');
                updateStatus('report-status', false);
                updateButtons('Report', false);
            };
        }

        function disconnectReport() {
            if (reportWs) {
                reportWs.close();
            }
        }

        function sendReportMessage() {
            const message = document.getElementById('report-message').value;
            if (reportWs && message) {
                reportWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('report-messages', 'user', message);
                document.getElementById('report-message').value = '';
            }
        }

        // Full Analysis WebSocket
        function connectFull() {
            const userId = document.getElementById('full-user-id').value;
            const company = document.getElementById('full-company').value;
            fullWs = new WebSocket(`ws://localhost:8000/ws/full_analysis/${userId}?company=${encodeURIComponent(company)}`);
            
            fullWs.onopen = function() {
                addMessage('full-messages', 'system', 'Connected to Full Analysis endpoint');
                updateStatus('full-status', true);
                updateButtons('Full', true);
            };
            
            fullWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('full-messages', data);
            };
            
            fullWs.onclose = function() {
                addMessage('full-messages', 'system', 'Disconnected from Full Analysis endpoint');
                updateStatus('full-status', false);
                updateButtons('Full', false);
            };
        }

        function disconnectFull() {
            if (fullWs) {
                fullWs.close();
            }
        }

        function sendFullMessage() {
            const message = document.getElementById('full-message').value;
            if (fullWs && message) {
                fullWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('full-messages', 'user', message);
                document.getElementById('full-message').value = '';
            }
        }

        // Orchestration WebSocket
        function connectOrchestration() {
            const userId = document.getElementById('orchestration-user-id').value;
            const company = document.getElementById('orchestration-company').value;
            const mode = document.getElementById('orchestration-mode').value;
            orchestrationWs = new WebSocket(`ws://localhost:8000/ws/orchestration/${userId}?company=${encodeURIComponent(company)}&mode=${mode}`);
            
            orchestrationWs.onopen = function() {
                addMessage('orchestration-messages', 'system', `Connected to Advanced Orchestration endpoint (Mode: ${mode})`);
                updateStatus('orchestration-status', true);
                updateButtons('Orchestration', true);
            };
            
            orchestrationWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('orchestration-messages', data);
            };
            
            orchestrationWs.onclose = function() {
                addMessage('orchestration-messages', 'system', 'Disconnected from Advanced Orchestration endpoint');
                updateStatus('orchestration-status', false);
                updateButtons('Orchestration', false);
            };
        }

        function disconnectOrchestration() {
            if (orchestrationWs) {
                orchestrationWs.close();
            }
        }

        function sendOrchestrationMessage() {
            const message = document.getElementById('orchestration-message').value;
            if (orchestrationWs && message) {
                orchestrationWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('orchestration-messages', 'user', message);
                document.getElementById('orchestration-message').value = '';
            }
        }

        // Demo Scenarios WebSocket
        function connectDemo() {
            const userId = document.getElementById('demo-user-id').value;
            const scenario = document.getElementById('demo-scenario').value;
            demoWs = new WebSocket(`ws://localhost:8000/ws/demo/${userId}?scenario=${scenario}`);

            demoWs.onopen = function() {
                addMessage('demo-messages', 'system', `Connected to Demo Scenario ${scenario} endpoint`);
                updateStatus('demo-status', true);
                updateButtons('Demo', true);
            };

            demoWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('demo-messages', data);
            };

            demoWs.onclose = function() {
                addMessage('demo-messages', 'system', 'Disconnected from Demo Scenario endpoint');
                updateStatus('demo-status', false);
                updateButtons('Demo', false);
            };
        }

        function disconnectDemo() {
            if (demoWs) {
                demoWs.close();
            }
        }

        function runDemoScenario() {
            if (demoWs) {
                const scenario = document.getElementById('demo-scenario').value;
                const scenarioNames = {
                    '1': 'Critical Database Shortage Analysis',
                    '2': 'Supplier Risk Alert with SLA Breach',
                    '3': 'Urgent Customer Priority Alert',
                    '4': 'Multi-Order Critical Alert'
                };

                addMessage('demo-messages', 'user', `Running Demo Scenario ${scenario}: ${scenarioNames[scenario]}`);

                // Demo scenarios are automatically executed upon connection
                // This button just provides user feedback
                addMessage('demo-messages', 'system', 'Demo scenario execution initiated...');
            }
        }

        // Enter key support for message inputs
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                if (e.target.id === 'research-message') sendResearchMessage();
                else if (e.target.id === 'analyze-message') sendAnalyzeMessage();
                else if (e.target.id === 'report-message') sendReportMessage();
                else if (e.target.id === 'full-message') sendFullMessage();
                else if (e.target.id === 'orchestration-message') sendOrchestrationMessage();
            }
        });
    </script>
</body>
</html>
    """)


async def handle_websocket_connection(websocket: WebSocket, user_id: str, session_type: SessionType, company_name: str):
    """Common WebSocket connection handler for all endpoints."""
    connection_id = f"{user_id}:{session_type.value}:{company_name}"
    logger.info(f"WebSocket connection attempt - ID: {connection_id}")
    
    try:
        logger.debug(f"Accepting WebSocket connection for {connection_id}")
        await websocket.accept()
        log_websocket_state(websocket, connection_id, "after accept()")
        logger.info(f"WebSocket connection accepted for {connection_id}")
    except Exception as e:
        logger.error(f"Failed to accept WebSocket connection for {connection_id}: {e}", exc_info=True)
        log_websocket_state(websocket, connection_id, "after accept() failure")
        return

    try:
        # Get or create user session
        logger.debug(f"Getting or creating session for {connection_id}")
        user_session = await session_manager.get_or_create_session(user_id, session_type, company_name)
        logger.info(f"Session created/retrieved - ID: {user_session.session_id} for connection: {connection_id}")

        # Set execution mode for orchestration sessions
        if session_type == SessionType.ORCHESTRATION and hasattr(websocket, 'state') and hasattr(websocket.state, 'execution_mode'):
            user_session.execution_mode = websocket.state.execution_mode
            logger.info(f"Set execution mode for orchestration session: {websocket.state.execution_mode}")

        # Send welcome message
        welcome_msg = {
            "type": "system",
            "message": f"Welcome to {session_type.value} analysis for {company_name}! Session ID: {user_session.session_id}",
            "user_id": user_id,
        }
        logger.debug(f"Sending welcome message to {connection_id}: {welcome_msg}")
        await websocket.send_text(json.dumps(welcome_msg))
        log_websocket_state(websocket, connection_id, "after welcome message sent")

        message_count = 0
        while True:
            try:
                # Check connection state before attempting to receive
                log_websocket_state(websocket, connection_id, "before receive_text()")
                if not is_websocket_connected(websocket):
                    logger.warning(f"WebSocket disconnected for {connection_id}, exiting message loop")
                    break
                
                # Receive message from client with immediate error handling
                logger.debug(f"Waiting for message from {connection_id}")
                try:
                    data = await websocket.receive_text()
                except RuntimeError as runtime_error:
                    error_msg = str(runtime_error)
                    if "WebSocket is not connected" in error_msg or "accept" in error_msg:
                        logger.warning(f"WebSocket connection lost during receive for {connection_id}: {error_msg}")
                        break
                    else:
                        # Re-raise if it's a different RuntimeError
                        raise
                except WebSocketDisconnect:
                    logger.info(f"WebSocket client disconnected during receive for {connection_id}")
                    break
                message_count += 1
                logger.debug(f"Received message #{message_count} from {connection_id}: {data[:100]}{'...' if len(data) > 100 else ''}")
                
                try:
                    message_data = json.loads(data)
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON received from {connection_id}: {e}")
                    await websocket.send_text(
                        json.dumps({"type": "error", "message": "Invalid JSON format"})
                    )
                    continue

                user_message = message_data.get("message", "")
                use_streaming = message_data.get("streaming", True)  # Default to streaming

                if not user_message:
                    logger.debug(f"Empty message received from {connection_id}, skipping")
                    continue

                logger.info(f"Processing message from {connection_id}: '{user_message}', streaming: {use_streaming}")

                if use_streaming:
                    # Send stream start message
                    stream_start_msg = {
                        "type": "stream_start",
                        "message": f"Starting {session_type.value} analysis...",
                        "user_id": user_id,
                    }
                    logger.debug(f"Sending stream start message to {connection_id}")
                    await websocket.send_text(json.dumps(stream_start_msg))

                    # Process message with streaming
                    logger.debug(f"Processing message with streaming through session {user_session.session_id}")
                    start_time = datetime.now()

                    try:
                        # Define streaming callback for LLM chunks with connection checking
                        async def stream_callback(chunk: str):
                            try:
                                if is_websocket_connected(websocket):
                                    chunk_msg = {
                                        "type": "stream_chunk",
                                        "message": chunk,
                                        "user_id": user_id,
                                    }
                                    await websocket.send_text(json.dumps(chunk_msg))
                                else:
                                    log_websocket_state(websocket, connection_id, "during LLM stream_callback - connection lost")
                            except WebSocketDisconnect:
                                logger.info(f"Client disconnected during LLM streaming to {connection_id}")
                                # Don't raise - just stop sending chunks
                            except Exception as e:
                                logger.debug(f"Failed to send stream chunk to {connection_id}: {e}")
                                log_websocket_state(websocket, connection_id, "after LLM stream_callback error")

                        # Define streaming callback for tool calls with connection checking
                        # Use different event types based on session type
                        async def tool_stream_callback(tool_message: dict):
                            try:
                                if is_websocket_connected(websocket):
                                    # Research sessions use "tool_stream", others use "mcp_tool_stream"
                                    event_type = "tool_stream" if session_type == SessionType.RESEARCH else "mcp_tool_stream"
                                    tool_msg = {
                                        "type": event_type,
                                        "data": tool_message,
                                        "user_id": user_id,
                                    }
                                    await websocket.send_text(json.dumps(tool_msg))
                                else:
                                    log_websocket_state(websocket, connection_id, "during tool stream_callback - connection lost")
                            except WebSocketDisconnect:
                                logger.info(f"Client disconnected during tool streaming to {connection_id}")
                                # Don't raise - just stop sending tool updates
                            except Exception as e:
                                logger.debug(f"Failed to send tool stream to {connection_id}: {e}")
                                log_websocket_state(websocket, connection_id, "after tool stream_callback error")

                        # Set tool streaming callback on the session
                        user_session.set_mcp_stream_callback(tool_stream_callback)

                        # Process message through appropriate agent with streaming
                        response = await user_session.process_message_streaming(user_message, stream_callback)
                        processing_time = (datetime.now() - start_time).total_seconds()
                        logger.info(f"Streaming response completed for {connection_id} in {processing_time:.2f}s")
                        logger.debug(f"Response length: {len(response)} characters")

                        # Send stream end message with connection checking
                        try:
                            if is_websocket_connected(websocket):
                                stream_end_msg = {
                                    "type": "stream_end",
                                    "message": "Analysis complete",
                                    "full_response": response,
                                    "user_id": user_id,
                                }
                                logger.debug(f"Sending stream end message to {connection_id}")
                                await websocket.send_text(json.dumps(stream_end_msg))
                                logger.info(f"Stream ended for {connection_id}")
                            else:
                                log_websocket_state(websocket, connection_id, "stream end - connection lost")
                        except WebSocketDisconnect:
                            logger.info(f"Client disconnected before stream end message could be sent to {connection_id}")
                            break
                        except Exception as e:
                            logger.debug(f"Failed to send stream end message to {connection_id}: {e}")
                            log_websocket_state(websocket, connection_id, "after stream end error")

                    except Exception as e:
                        processing_time = (datetime.now() - start_time).total_seconds()
                        logger.error(f"Error processing streaming message for {connection_id} after {processing_time:.2f}s: {e}", exc_info=True)
                        try:
                            if is_websocket_connected(websocket):
                                error_msg = {
                                    "type": "error",
                                    "message": f"Error processing your request: {str(e)}",
                                }
                                await websocket.send_text(json.dumps(error_msg))
                            else:
                                log_websocket_state(websocket, connection_id, "streaming error handling - connection lost")
                        except Exception as send_error:
                            logger.debug(f"Failed to send error message to {connection_id}: {send_error}")
                            log_websocket_state(websocket, connection_id, "after streaming error message send failure")
                else:
                    # Non-streaming mode (original behavior)
                    # Send progress message
                    progress_msg = {
                        "type": "progress",
                        "message": f"Processing {session_type.value} request...",
                    }
                    logger.debug(f"Sending progress message to {connection_id}")
                    await websocket.send_text(json.dumps(progress_msg))

                    # Process message through appropriate agent
                    logger.debug(f"Processing message through session {user_session.session_id}")
                    start_time = datetime.now()
                    try:
                        response = await user_session.process_message(user_message)
                        processing_time = (datetime.now() - start_time).total_seconds()
                        logger.info(f"Message processed successfully for {connection_id} in {processing_time:.2f}s")
                        logger.debug(f"Response length: {len(response)} characters")
                    except Exception as e:
                        processing_time = (datetime.now() - start_time).total_seconds()
                        logger.error(f"Error processing message for {connection_id} after {processing_time:.2f}s: {e}", exc_info=True)
                        response = f"Error processing your request: {str(e)}"

                    # Send response back to client with connection checking
                    try:
                        if is_websocket_connected(websocket):
                            response_msg = {
                                "type": "result",
                                "message": response,
                                "user_id": user_id,
                            }
                            logger.debug(f"Sending response to {connection_id}: {len(response)} characters")
                            await websocket.send_text(json.dumps(response_msg))
                            logger.info(f"Response sent successfully to {connection_id}")
                        else:
                            log_websocket_state(websocket, connection_id, "non-streaming response - connection lost")
                    except WebSocketDisconnect:
                        logger.info(f"Client disconnected while sending response to {connection_id}")
                        break
                    except Exception as send_error:
                        logger.debug(f"Failed to send response to {connection_id}: {send_error}")
                        log_websocket_state(websocket, connection_id, "after response send failure")

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for {connection_id}")
                break
            except Exception as e:
                logger.error(f"Error in message loop for {connection_id}: {e}", exc_info=True)
                log_websocket_state(websocket, connection_id, "after message loop error")
                
                # Check if it's a WebSocket connection error specifically
                if "WebSocket is not connected" in str(e) or "close message has been sent" in str(e):
                    logger.warning(f"WebSocket connection lost for {connection_id}: {e}")
                    break
                
                try:
                    if is_websocket_connected(websocket):
                        error_msg = {"type": "error", "message": f"An error occurred: {str(e)}"}
                        await websocket.send_text(json.dumps(error_msg))
                    else:
                        log_websocket_state(websocket, connection_id, "message loop error - connection lost, cannot send error")
                except Exception as send_error:
                    logger.error(f"Failed to send error message to {connection_id}: {send_error}")
                    log_websocket_state(websocket, connection_id, "after error message send failure")
                
                # Break on connection-related errors
                if "connection" in str(e).lower() or not is_websocket_connected(websocket):
                    logger.warning(f"Connection-related error for {connection_id}, breaking loop")
                    break

    except Exception as e:
        logger.error(f"Session error for {connection_id}: {e}", exc_info=True)
        log_websocket_state(websocket, connection_id, "after session error")
        try:
            if is_websocket_connected(websocket):
                await websocket.send_text(
                    json.dumps({"type": "error", "message": f"Session error: {str(e)}"})
                )
            else:
                log_websocket_state(websocket, connection_id, "session error - connection lost, cannot send error")
        except Exception as send_error:
            logger.error(f"Failed to send session error message to {connection_id}: {send_error}")
            log_websocket_state(websocket, connection_id, "after session error message send failure")
    finally:
        # Clean up session if needed
        logger.info(f"Cleaning up session for {connection_id}")
        log_websocket_state(websocket, connection_id, "during cleanup")
        try:
            await session_manager.cleanup_session(user_id, session_type)
            logger.debug(f"Session cleanup completed for {connection_id}")
        except Exception as e:
            logger.error(f"Error during session cleanup for {connection_id}: {e}", exc_info=True)


@app.websocket("/ws/{user_id}")
async def basic_endpoint(websocket: WebSocket, user_id: str):
    """Basic WebSocket endpoint for general purpose interactions."""
    logger.info(f"Basic endpoint accessed - User: {user_id}")
    await handle_websocket_connection(websocket, user_id, SessionType.BASIC, "General")


@app.websocket("/ws/research/{user_id}")
async def research_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for financial research functionality."""
    company = websocket.query_params.get("company", "Apple Inc.")
    logger.info(f"Research endpoint accessed - User: {user_id}, Company: {company}")
    await handle_websocket_connection(websocket, user_id, SessionType.RESEARCH, company)


@app.websocket("/ws/analyze/{user_id}")
async def analyze_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for financial analysis operations."""
    company = websocket.query_params.get("company", "Apple Inc.")
    logger.info(f"Analyze endpoint accessed - User: {user_id}, Company: {company}")
    await handle_websocket_connection(websocket, user_id, SessionType.ANALYZE, company)


@app.websocket("/ws/report/{user_id}")
async def report_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for generating financial reports."""
    company = websocket.query_params.get("company", "Apple Inc.")
    logger.info(f"Report endpoint accessed - User: {user_id}, Company: {company}")
    await handle_websocket_connection(websocket, user_id, SessionType.REPORT, company)


@app.websocket("/ws/full_analysis/{user_id}")
async def full_analysis_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for comprehensive end-to-end financial analysis."""
    company = websocket.query_params.get("company", "Apple Inc.")
    logger.info(f"Full analysis endpoint accessed - User: {user_id}, Company: {company}")
    await handle_websocket_connection(websocket, user_id, SessionType.FULL_ANALYSIS, company)


@app.websocket("/ws/orchestration/{user_id}")
async def orchestration_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for advanced orchestrated financial analysis workflows."""
    company = websocket.query_params.get("company", "Apple Inc.")
    mode = websocket.query_params.get("mode", "orchestrator_based")  # "pattern_based" or "orchestrator_based"
    logger.info(f"Orchestration endpoint accessed - User: {user_id}, Company: {company}, Mode: {mode}")

    # Store the execution mode in the connection for later use
    websocket.state.execution_mode = mode
    await handle_websocket_connection(websocket, user_id, SessionType.ORCHESTRATION, company)


@app.websocket("/ws/demo/{user_id}")
async def demo_scenario_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for running predefined demo scenarios."""
    scenario_num = websocket.query_params.get("scenario", "1")
    logger.info(f"Demo scenario endpoint accessed - User: {user_id}, Scenario: {scenario_num}")

    connection_id = f"{user_id}:demo:{scenario_num}"
    logger.info(f"WebSocket demo connection attempt - ID: {connection_id}")

    try:
        await websocket.accept()
        logger.info(f"WebSocket demo connection accepted for {connection_id}")

        # Send welcome message
        welcome_msg = {
            "type": "system",
            "message": f"Welcome to Demo Scenario {scenario_num}! Preparing to execute...",
            "user_id": user_id,
        }
        await websocket.send_text(json.dumps(welcome_msg))

        # Validate scenario number
        try:
            scenario_int = int(scenario_num)
            if scenario_int < 1 or scenario_int > 4:
                raise ValueError("Invalid scenario number")
        except ValueError:
            error_msg = {
                "type": "error",
                "message": f"Invalid scenario number: {scenario_num}. Must be 1-4.",
            }
            await websocket.send_text(json.dumps(error_msg))
            return

        # Check if orchestration is available
        if not session_manager.orchestration_runner:
            error_msg = {
                "type": "error",
                "message": "Orchestration system not available. Please ensure all MCP servers are running.",
            }
            await websocket.send_text(json.dumps(error_msg))
            return

        # Send stream start message
        stream_start_msg = {
            "type": "stream_start",
            "message": f"Starting demo scenario {scenario_num}...",
            "user_id": user_id,
        }
        await websocket.send_text(json.dumps(stream_start_msg))

        # Define streaming callback for demo execution
        async def demo_stream_callback(chunk: str):
            try:
                if is_websocket_connected(websocket):
                    chunk_msg = {
                        "type": "stream_chunk",
                        "message": chunk,
                        "user_id": user_id,
                    }
                    await websocket.send_text(json.dumps(chunk_msg))
            except WebSocketDisconnect:
                logger.info(f"Client disconnected during demo streaming to {connection_id}")
            except Exception as e:
                logger.debug(f"Failed to send demo stream chunk to {connection_id}: {e}")

        # Execute the demo scenario
        result = await session_manager.execute_demo_scenario(scenario_int, demo_stream_callback)

        # Send completion message
        if is_websocket_connected(websocket):
            stream_end_msg = {
                "type": "stream_end",
                "message": "Demo scenario complete",
                "full_response": json.dumps(result, indent=2, default=str),
                "user_id": user_id,
            }
            await websocket.send_text(json.dumps(stream_end_msg))
            logger.info(f"Demo scenario {scenario_num} completed for {connection_id}")

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for demo {connection_id}")
    except Exception as e:
        logger.error(f"Demo scenario error for {connection_id}: {e}", exc_info=True)
        try:
            if is_websocket_connected(websocket):
                await websocket.send_text(
                    json.dumps({"type": "error", "message": f"Demo scenario error: {str(e)}"})
                )
        except Exception as send_error:
            logger.error(f"Failed to send demo error message to {connection_id}: {send_error}")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    logger.debug("Health check endpoint called")
    active_sessions = len(session_manager.sessions)
    logger.debug(f"Active sessions count: {active_sessions}")
    
    # Check orchestration runner status
    orchestration_status = {
        "available": session_manager.orchestration_runner is not None,
        "execution_stats": None
    }
    
    if session_manager.orchestration_runner:
        try:
            orchestration_status["execution_stats"] = session_manager.orchestration_runner.get_execution_statistics()
        except Exception as e:
            logger.warning(f"Failed to get orchestration stats: {e}")
            orchestration_status["error"] = str(e)
    
    health_data = {
        "status": "healthy", 
        "active_sessions": active_sessions,
        "endpoints": ["research", "analyze", "report", "full_analysis", "orchestration"],
        "orchestration": orchestration_status,
        "timestamp": datetime.now().isoformat()
    }
    logger.debug(f"Returning health check data: {health_data}")
    return health_data


@app.get("/orchestration/health")
async def orchestration_health_check():
    """Detailed health check for orchestration system."""
    logger.debug("Orchestration health check endpoint called")

    if not session_manager.orchestration_runner:
        return {
            "status": "unavailable",
            "message": "Orchestration runner not initialized",
            "timestamp": datetime.now().isoformat()
        }

    try:
        health_info = await session_manager.run_orchestration_health_check()
        return health_info
    except Exception as e:
        logger.error(f"Orchestration health check failed: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/orchestration/statistics")
async def get_orchestration_statistics():
    """Get orchestration execution statistics."""
    logger.debug("Orchestration statistics endpoint called")

    try:
        stats = session_manager.get_orchestration_statistics()
        return {
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get orchestration statistics: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/orchestration/query-types")
async def get_supported_query_types():
    """Get list of supported query types."""
    logger.debug("Supported query types endpoint called")

    try:
        query_types = session_manager.get_supported_query_types()
        return {
            "supported_query_types": query_types,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get supported query types: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.get("/orchestration/patterns")
async def get_available_patterns():
    """Get available workflow patterns."""
    logger.debug("Available patterns endpoint called")

    try:
        patterns = session_manager.get_available_patterns()
        return {
            "available_patterns": patterns,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get available patterns: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@app.post("/orchestration/demo/{scenario_num}")
async def execute_demo_scenario_rest(scenario_num: int):
    """Execute a demo scenario via REST API (non-streaming)."""
    logger.debug(f"Demo scenario REST endpoint called - Scenario: {scenario_num}")

    if scenario_num < 1 or scenario_num > 4:
        raise HTTPException(status_code=400, detail=f"Invalid scenario number: {scenario_num}. Must be 1-4.")

    if not session_manager.orchestration_runner:
        raise HTTPException(status_code=503, detail="Orchestration system not available")

    try:
        result = await session_manager.execute_demo_scenario(scenario_num)
        return {
            "scenario_number": scenario_num,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Demo scenario {scenario_num} execution failed: {e}")
        raise HTTPException(status_code=500, detail=f"Demo scenario execution failed: {str(e)}")


@app.post("/orchestration/query")
async def execute_orchestration_query(query_data: Dict[str, Any]):
    """Execute an orchestration query via REST API (non-streaming)."""
    logger.debug("Orchestration query REST endpoint called")

    if not session_manager.orchestration_runner:
        raise HTTPException(status_code=503, detail="Orchestration system not available")

    query = query_data.get("query")
    if not query:
        raise HTTPException(status_code=400, detail="Query is required")

    execution_mode = query_data.get("execution_mode", "pattern_based")
    if execution_mode not in ["pattern_based", "orchestrator_based"]:
        raise HTTPException(status_code=400, detail="Invalid execution_mode. Must be 'pattern_based' or 'orchestrator_based'")

    try:
        result = await session_manager.orchestration_runner.execute_financial_query(
            query=query,
            execution_mode=execution_mode
        )
        return {
            "query": query,
            "execution_mode": execution_mode,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Orchestration query execution failed: {e}")
        raise HTTPException(status_code=500, detail=f"Query execution failed: {str(e)}")


@app.get("/sessions")
async def list_sessions():
    """List active sessions."""
    logger.debug("Listing active sessions endpoint called")
    sessions_info = {}
    
    for session_key, session in session_manager.sessions.items():
        session_info = {
            "user_id": session.user_id,
            "session_type": session.session_type.value,
            "company_name": session.company_name,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "message_count": len(session.message_history),
        }
        sessions_info[session_key] = session_info
        logger.debug(f"Session {session_key}: {session_info}")
    
    result = {
        "active_sessions": sessions_info,
        "total_sessions": len(session_manager.sessions),
    }
    logger.info(f"Returning {len(sessions_info)} active sessions")
    return result


@app.get("/sessions/{user_id}/{session_type}")
async def get_session_info(user_id: str, session_type: str):
    """Get information about a specific session."""
    logger.debug(f"Getting session info for user: {user_id}, type: {session_type}")
    
    try:
        session_type_enum = SessionType(session_type)
        session_info = session_manager.get_session_info(user_id, session_type_enum)
        
        if session_info:
            logger.debug(f"Found session info for {user_id}:{session_type}")
            return session_info
        else:
            logger.warning(f"Session not found for {user_id}:{session_type}")
            return {"error": "Session not found"}
            
    except ValueError as e:
        logger.error(f"Invalid session type '{session_type}' for user {user_id}: {e}")
        return {"error": f"Invalid session type: {session_type}"}


if __name__ == "__main__":
    logger.info("Starting MCP Financial Analyzer WebSocket Server")
    logger.info("Server configuration: host=0.0.0.0, port=8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)

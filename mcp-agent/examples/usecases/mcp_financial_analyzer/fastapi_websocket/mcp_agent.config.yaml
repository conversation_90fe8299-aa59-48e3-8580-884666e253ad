$schema: ../../../schema/mcp-agent.config.schema.json

# Configuration for Financial Analyzer WebSocket Server
execution_engine: asyncio
logger:
  transports: [file]
  level: debug
  progress_display: true
  path_settings:
    path_pattern: "logs/mcp-agent-{unique_id}.jsonl"
    unique_id: "timestamp" # Options: "timestamp" or "session_id"
    timestamp_format: "%Y%m%d_%H%M%S"

# MCP server configurations
mcp:
  servers:
    # Fetch server for basic web retrieval
    fetch:
      command: "uvx"
      args: ["mcp-server-fetch"]
    
    # Google Search MCP server
    g-search:
      command: "npx"
      args: ["-y", "g-search-mcp"]
    
    # Filesystem server for writing reports
    filesystem:
      command: "npx"
      args: ["-y", "@modelcontextprotocol/server-filesystem"]
    
    shortage-index:
      transport: sse
      url: "http://localhost:6970/sse"

    # Agent Develop alert notification server  
    alert-notification:
      transport: "sse"
      url: "http://localhost:6972/sse"
    
    # MySQL database server
    mysql:
      transport: "sse"
      url: "http://localhost:8702/sse"

# LLM Provider configurations
# VLLM configuration (primary)
vllm:
  api_base: "http://************:38701/v1"
  default_model: "Qwen/Qwen3-32B"
  api_key: "EMPTY"  # vLLM doesn't require an API key by default

# OpenAI configuration (fallback)
openai:
  default_model: gpt-4o-mini


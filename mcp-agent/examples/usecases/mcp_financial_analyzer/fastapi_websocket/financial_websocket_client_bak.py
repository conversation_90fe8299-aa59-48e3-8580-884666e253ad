#!/usr/bin/env python3
"""
Comprehensive WebSocket Client for Financial Analysis Orchestration

This client demonstrates the newly integrated orchestration functionality including:
- Demo scenario execution with real-time streaming
- Enhanced orchestration with execution mode support
- Interactive command-line interface
- Comprehensive error handling and logging

Usage:
    python financial_websocket_client.py
"""

import asyncio
import json
import logging
import sys
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException
import argparse

# Try to import colorama for colored output, fallback to no colors if not available
try:
    import colorama
    from colorama import Fore, Back, Style
    colorama.init(autoreset=True)
    COLORS_AVAILABLE = True
except ImportError:
    # Fallback color definitions if colorama is not available
    class Fore:
        CYAN = YELLOW = GREEN = WHITE = MAGENTA = RED = BLUE = ""
    COLORS_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f'websocket_client_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    ]
)
logger = logging.getLogger(__name__)


class FinancialWebSocketClient:
    """Comprehensive WebSocket client for financial analysis orchestration."""

    def __init__(self, base_url: str = "ws://localhost:8000"):
        self.base_url = base_url
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.user_id = f"client_{uuid.uuid4().hex[:8]}"

        # Demo scenario definitions
        self.demo_scenarios = {
            1: {
                "name": "Critical Database Shortage Analysis with Customer Alert",
                "description": "Triggers MySQL → Shortage Analysis → Alert Manager pipeline",
                "expected_flow": "Database query → Shortage calculation → Customer notification"
            },
            2: {
                "name": "Supplier Risk Alert with SLA Breach Notification",
                "description": "Tests database → analysis → customer notification workflow",
                "expected_flow": "Supplier inventory check → Risk analysis → SLA breach alerts"
            },
            3: {
                "name": "Urgent Customer Priority Alert Analysis",
                "description": "Database check → shortage analysis → priority customer alerts",
                "expected_flow": "Priority order check → Critical shortage detection → Customer escalation"
            },
            4: {
                "name": "Multi-Order Critical Alert Orchestration",
                "description": "Full pipeline: database → analysis → alert orchestration",
                "expected_flow": "Multi-order analysis → Weighted shortage indices → Comprehensive alerting"
            }
        }

    def print_banner(self):
        """Print application banner."""
        print(f"\n{Fore.CYAN}{'='*80}")
        print(f"{Fore.CYAN}🚀 Financial Analysis Orchestration WebSocket Client")
        print(f"{Fore.CYAN}{'='*80}")
        print(f"{Fore.GREEN}User ID: {self.user_id}")
        print(f"{Fore.GREEN}Server: {self.base_url}")
        print(f"{Fore.GREEN}Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{Fore.CYAN}{'='*80}\n")

    def print_demo_scenarios(self):
        """Print available demo scenarios."""
        print(f"{Fore.YELLOW}📋 Available Demo Scenarios:")
        print(f"{Fore.YELLOW}{'-'*50}")

        for scenario_num, scenario in self.demo_scenarios.items():
            print(f"{Fore.WHITE}{scenario_num}. {Fore.CYAN}{scenario['name']}")
            print(f"   {Fore.GREEN}Description: {scenario['description']}")
            print(f"   {Fore.BLUE}Flow: {scenario['expected_flow']}")
            print()

    def format_message(self, message_type: str, content: str, timestamp: str = None) -> str:
        """Format WebSocket messages with colors and structure."""
        if timestamp is None:
            timestamp = datetime.now().strftime('%H:%M:%S')

        color_map = {
            'system': Fore.CYAN,
            'stream_start': Fore.GREEN,
            'stream_chunk': Fore.WHITE,
            'stream_end': Fore.MAGENTA,
            'error': Fore.RED,
            'user': Fore.YELLOW,
            'success': Fore.GREEN,
            'info': Fore.BLUE
        }

        color = color_map.get(message_type, Fore.WHITE)
        icon_map = {
            'system': '🔧',
            'stream_start': '🚀',
            'stream_chunk': '📡',
            'stream_end': '✅',
            'error': '❌',
            'user': '👤',
            'success': '✅',
            'info': 'ℹ️'
        }

        icon = icon_map.get(message_type, '📝')
        return f"{color}[{timestamp}] {icon} {message_type.upper()}: {content}"

    async def connect_websocket(self, endpoint: str) -> bool:
        """Connect to WebSocket endpoint with error handling."""
        try:
            full_url = f"{self.base_url}{endpoint}"
            logger.info(f"Connecting to: {full_url}")
            print(self.format_message('info', f"Connecting to: {full_url}"))

            self.websocket = await websockets.connect(
                full_url,
                ping_interval=20,
                ping_timeout=10,
                close_timeout=10
            )

            print(self.format_message('success', f"Connected successfully to {endpoint}"))
            logger.info(f"WebSocket connected to {endpoint}")
            return True

        except ConnectionRefusedError:
            error_msg = f"Connection refused. Is the server running on {self.base_url}?"
            print(self.format_message('error', error_msg))
            logger.error(error_msg)
            return False
        except Exception as e:
            error_msg = f"Connection failed: {str(e)}"
            print(self.format_message('error', error_msg))
            logger.error(error_msg)
            return False

    async def disconnect_websocket(self):
        """Gracefully disconnect WebSocket."""
        if self.websocket:
            try:
                await self.websocket.close()
                print(self.format_message('info', "WebSocket disconnected"))
                logger.info("WebSocket disconnected")
            except Exception as e:
                logger.error(f"Error during disconnect: {e}")
            finally:
                self.websocket = None

    async def send_message(self, message: str):
        """Send a message to the connected WebSocket."""
        if not self.websocket:
            print(self.format_message('error', "Not connected to any endpoint"))
            return

        try:
            await self.websocket.send(json.dumps({"message": message, "streaming": True}))
            print(self.format_message('user', f"Sent: {message}"))
            logger.info(f"Message sent: {message}")
        except Exception as e:
            error_msg = f"Failed to send message: {str(e)}"
            print(self.format_message('error', error_msg))
            logger.error(error_msg)
    
    async def listen_for_messages(self) -> Dict[str, Any]:
        """Listen for WebSocket messages and display them in real-time."""
        if not self.websocket:
            return {"success": False, "error": "No WebSocket connection"}

        messages_received = []
        final_result = None

        try:
            print(self.format_message('info', "Listening for messages... (Press Ctrl+C to stop)"))

            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    messages_received.append(data)

                    message_type = data.get('type', 'unknown')
                    message_content = data.get('message', str(data))
                    user_id = data.get('user_id', 'unknown')

                    # Display the message with appropriate formatting
                    if message_type == 'stream_chunk':
                        # Stream chunks might contain multiple lines, handle them properly
                        lines = message_content.split('\n')
                        for line in lines:
                            if line.strip():
                                print(self.format_message('stream_chunk', line.strip()))
                    else:
                        print(self.format_message(message_type, message_content))

                    # Check for completion
                    if message_type == 'stream_end':
                        final_result = data.get('full_response')
                        print(self.format_message('success', "Stream completed"))
                        break
                    elif message_type == 'error':
                        print(self.format_message('error', f"Server error: {message_content}"))
                        break

                except json.JSONDecodeError as e:
                    print(self.format_message('error', f"Failed to parse message: {message}"))
                    logger.error(f"JSON decode error: {e}")
                except Exception as e:
                    print(self.format_message('error', f"Error processing message: {str(e)}"))
                    logger.error(f"Message processing error: {e}")

        except ConnectionClosed:
            print(self.format_message('info', "Connection closed by server"))
            logger.info("WebSocket connection closed by server")
        except KeyboardInterrupt:
            print(self.format_message('info', "Interrupted by user"))
            logger.info("WebSocket listening interrupted by user")
        except Exception as e:
            print(self.format_message('error', f"Error during message listening: {str(e)}"))
            logger.error(f"Message listening error: {e}")

        return {
            "success": True,
            "messages_received": len(messages_received),
            "final_result": final_result,
            "all_messages": messages_received
        }

    async def run_demo_scenario(self, scenario_num: int) -> Dict[str, Any]:
        """Run a specific demo scenario."""
        if scenario_num not in self.demo_scenarios:
            error_msg = f"Invalid scenario number: {scenario_num}. Must be 1-4."
            print(self.format_message('error', error_msg))
            return {"success": False, "error": error_msg}

        scenario = self.demo_scenarios[scenario_num]

        print(f"\n{Fore.YELLOW}{'='*60}")
        print(f"{Fore.YELLOW}🎭 Running Demo Scenario {scenario_num}")
        print(f"{Fore.YELLOW}{'='*60}")
        print(f"{Fore.CYAN}Name: {scenario['name']}")
        print(f"{Fore.GREEN}Description: {scenario['description']}")
        print(f"{Fore.BLUE}Expected Flow: {scenario['expected_flow']}")
        print(f"{Fore.YELLOW}{'='*60}\n")

        # Connect to demo scenario endpoint
        endpoint = f"/ws/demo/{self.user_id}?scenario={scenario_num}"

        if not await self.connect_websocket(endpoint):
            return {"success": False, "error": "Failed to connect to demo endpoint"}

        try:
            # Listen for messages (demo scenarios auto-execute upon connection)
            result = await self.listen_for_messages()

            print(f"\n{Fore.MAGENTA}{'='*60}")
            print(f"{Fore.MAGENTA}📊 Demo Scenario {scenario_num} Results")
            print(f"{Fore.MAGENTA}{'='*60}")
            print(f"{Fore.GREEN}Messages Received: {result.get('messages_received', 0)}")

            if result.get('final_result'):
                try:
                    final_data = json.loads(result['final_result']) if isinstance(result['final_result'], str) else result['final_result']
                    print(f"{Fore.CYAN}Final Result Summary:")
                    print(f"{Fore.WHITE}{json.dumps(final_data, indent=2)}")
                except:
                    print(f"{Fore.CYAN}Final Result: {result['final_result']}")

            print(f"{Fore.MAGENTA}{'='*60}\n")

            return result

        finally:
            await self.disconnect_websocket()

    async def run_enhanced_orchestration(self, query: str, execution_mode: str = "pattern_based") -> Dict[str, Any]:
        """Run enhanced orchestration with custom query and execution mode."""
        if execution_mode not in ["pattern_based", "orchestrator_based"]:
            error_msg = f"Invalid execution mode: {execution_mode}. Must be 'pattern_based' or 'orchestrator_based'."
            print(self.format_message('error', error_msg))
            return {"success": False, "error": error_msg}

        print(f"\n{Fore.YELLOW}{'='*60}")
        print(f"{Fore.YELLOW}🎼 Running Enhanced Orchestration")
        print(f"{Fore.YELLOW}{'='*60}")
        print(f"{Fore.CYAN}Query: {query}")
        print(f"{Fore.GREEN}Execution Mode: {execution_mode}")
        print(f"{Fore.YELLOW}{'='*60}\n")

        # Connect to orchestration endpoint
        endpoint = f"/ws/orchestration/{self.user_id}?mode={execution_mode}"

        if not await self.connect_websocket(endpoint):
            return {"success": False, "error": "Failed to connect to orchestration endpoint"}

        try:
            # Send the query
            await self.send_message(query)

            # Listen for messages
            result = await self.listen_for_messages()

            print(f"\n{Fore.MAGENTA}{'='*60}")
            print(f"{Fore.MAGENTA}📊 Enhanced Orchestration Results")
            print(f"{Fore.MAGENTA}{'='*60}")
            print(f"{Fore.GREEN}Messages Received: {result.get('messages_received', 0)}")
            print(f"{Fore.BLUE}Execution Mode: {execution_mode}")

            if result.get('final_result'):
                try:
                    final_data = json.loads(result['final_result']) if isinstance(result['final_result'], str) else result['final_result']
                    print(f"{Fore.CYAN}Final Result Summary:")
                    print(f"{Fore.WHITE}{json.dumps(final_data, indent=2)}")
                except:
                    print(f"{Fore.CYAN}Final Result: {result['final_result']}")

            print(f"{Fore.MAGENTA}{'='*60}\n")

            return result

        finally:
            await self.disconnect_websocket()

    async def disconnect(self):
        """Disconnect from the WebSocket (legacy method for compatibility)."""
        await self.disconnect_websocket()
    
    async def interactive_menu(self):
        """Run interactive menu for orchestration testing."""
        self.print_banner()

        while True:
            print(f"{Fore.CYAN}🎯 Main Menu:")
            print(f"{Fore.WHITE}1. Run Demo Scenario")
            print(f"{Fore.WHITE}2. Enhanced Orchestration (Custom Query)")
            print(f"{Fore.WHITE}3. View Demo Scenarios")
            print(f"{Fore.WHITE}4. Change User ID")
            print(f"{Fore.WHITE}5. Exit")

            try:
                choice = input(f"\n{Fore.YELLOW}Select option (1-5): ").strip()
            except (EOFError, KeyboardInterrupt):
                print(f"\n{Fore.CYAN}👋 Goodbye!")
                break

            if choice == '1':
                await self.demo_scenario_menu()
            elif choice == '2':
                await self.orchestration_menu()
            elif choice == '3':
                self.print_demo_scenarios()
                input(f"\n{Fore.CYAN}Press Enter to continue...")
            elif choice == '4':
                new_user_id = input(f"{Fore.YELLOW}Enter new User ID (current: {self.user_id}): ").strip()
                if new_user_id:
                    self.user_id = new_user_id
                    print(f"{Fore.GREEN}User ID updated to: {self.user_id}")
            elif choice == '5':
                print(f"{Fore.CYAN}👋 Goodbye!")
                break
            else:
                print(f"{Fore.RED}Invalid choice. Please select 1-5.")

            print()  # Add spacing between menu iterations

    async def demo_scenario_menu(self):
        """Interactive menu for demo scenarios."""
        self.print_demo_scenarios()

        try:
            scenario_input = input(f"\n{Fore.YELLOW}Select demo scenario (1-4) or 'back': ").strip()
        except (EOFError, KeyboardInterrupt):
            return

        if scenario_input.lower() == 'back':
            return

        try:
            scenario_num = int(scenario_input)
            if scenario_num in self.demo_scenarios:
                print(f"{Fore.GREEN}Starting Demo Scenario {scenario_num}...")
                result = await self.run_demo_scenario(scenario_num)

                if result.get('success'):
                    print(f"{Fore.GREEN}✅ Demo scenario completed successfully!")
                else:
                    print(f"{Fore.RED}❌ Demo scenario failed: {result.get('error', 'Unknown error')}")

                input(f"\n{Fore.CYAN}Press Enter to continue...")
            else:
                print(f"{Fore.RED}Invalid scenario number. Must be 1-4.")
        except ValueError:
            print(f"{Fore.RED}Invalid input. Please enter a number 1-4.")

    async def orchestration_menu(self):
        """Interactive menu for enhanced orchestration."""
        print(f"\n{Fore.CYAN}🎼 Enhanced Orchestration")
        print(f"{Fore.CYAN}{'-'*40}")

        try:
            query = input(f"{Fore.YELLOW}Enter your financial analysis query: ").strip()
            if not query:
                print(f"{Fore.RED}Query cannot be empty.")
                return

            print(f"\n{Fore.CYAN}Execution Modes:")
            print(f"{Fore.WHITE}1. Pattern-based (Recommended for streaming)")
            print(f"{Fore.WHITE}2. Orchestrator-based (Advanced reasoning)")

            mode_choice = input(f"{Fore.YELLOW}Select execution mode (1-2): ").strip()

            if mode_choice == '1':
                execution_mode = "pattern_based"
            elif mode_choice == '2':
                execution_mode = "orchestrator_based"
            else:
                print(f"{Fore.RED}Invalid choice. Using pattern_based mode.")
                execution_mode = "pattern_based"

            print(f"{Fore.GREEN}Starting Enhanced Orchestration...")
            result = await self.run_enhanced_orchestration(query, execution_mode)

            if result.get('success'):
                print(f"{Fore.GREEN}✅ Enhanced orchestration completed successfully!")
            else:
                print(f"{Fore.RED}❌ Enhanced orchestration failed: {result.get('error', 'Unknown error')}")

            input(f"\n{Fore.CYAN}Press Enter to continue...")

        except (EOFError, KeyboardInterrupt):
            return


async def run_single_demo(scenario_num: int, base_url: str = "ws://localhost:8000"):
    """Run a single demo scenario non-interactively."""
    client = FinancialWebSocketClient(base_url)

    print(f"🧪 Running Demo Scenario {scenario_num}")
    result = await client.run_demo_scenario(scenario_num)

    if result.get('success'):
        print(f"✅ Demo scenario {scenario_num} completed successfully!")
        return True
    else:
        print(f"❌ Demo scenario {scenario_num} failed: {result.get('error', 'Unknown error')}")
        return False

async def run_single_orchestration(query: str, execution_mode: str = "pattern_based", base_url: str = "ws://localhost:8000"):
    """Run a single orchestration query non-interactively."""
    client = FinancialWebSocketClient(base_url)

    print(f"🎼 Running Enhanced Orchestration")
    result = await client.run_enhanced_orchestration(query, execution_mode)

    if result.get('success'):
        print(f"✅ Enhanced orchestration completed successfully!")
        return True
    else:
        print(f"❌ Enhanced orchestration failed: {result.get('error', 'Unknown error')}")
        return False


async def main():
    """Main function to run the comprehensive orchestration client."""
    parser = argparse.ArgumentParser(
        description="Comprehensive WebSocket Client for Financial Analysis Orchestration",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Interactive mode (default)
  python financial_websocket_client.py

  # Run specific demo scenario
  python financial_websocket_client.py --demo 1

  # Run orchestration with custom query
  python financial_websocket_client.py --orchestration "Check inventory for CUSTORD-202506001"

  # Run all demo scenarios
  python financial_websocket_client.py --demo-all

  # Use different server
  python financial_websocket_client.py --url ws://*************:8000
        """
    )

    parser.add_argument("--url", default="ws://localhost:8000",
                       help="WebSocket server URL (default: ws://localhost:8000)")
    parser.add_argument("--demo", type=int, choices=[1, 2, 3, 4],
                       help="Run specific demo scenario (1-4)")
    parser.add_argument("--demo-all", action="store_true",
                       help="Run all demo scenarios sequentially")
    parser.add_argument("--orchestration", type=str,
                       help="Run orchestration with custom query")
    parser.add_argument("--execution-mode", choices=["pattern_based", "orchestrator_based"],
                       default="pattern_based", help="Orchestration execution mode")
    parser.add_argument("--user-id", type=str,
                       help="Custom user ID (default: auto-generated)")
    parser.add_argument("--no-interactive", action="store_true",
                       help="Disable interactive mode (for scripting)")

    args = parser.parse_args()

    # Create client
    client = FinancialWebSocketClient(args.url)

    # Set custom user ID if provided
    if args.user_id:
        client.user_id = args.user_id

    try:
        if args.demo:
            # Run specific demo scenario
            success = await run_single_demo(args.demo, args.url)
            sys.exit(0 if success else 1)

        elif args.demo_all:
            # Run all demo scenarios
            print(f"{Fore.CYAN}🧪 Running All Demo Scenarios")
            print(f"{Fore.CYAN}{'='*50}")

            results = []
            for scenario_num in range(1, 5):
                print(f"\n{Fore.YELLOW}--- Demo Scenario {scenario_num} ---")
                success = await run_single_demo(scenario_num, args.url)
                results.append((scenario_num, success))

                if scenario_num < 4:  # Don't wait after the last scenario
                    print(f"{Fore.BLUE}Waiting 3 seconds before next scenario...")
                    await asyncio.sleep(3)

            # Summary
            print(f"\n{Fore.MAGENTA}{'='*50}")
            print(f"{Fore.MAGENTA}📊 All Demo Scenarios Summary")
            print(f"{Fore.MAGENTA}{'='*50}")

            passed = 0
            for scenario_num, success in results:
                status = f"{Fore.GREEN}✅ PASSED" if success else f"{Fore.RED}❌ FAILED"
                print(f"{status}: Demo Scenario {scenario_num}")
                if success:
                    passed += 1

            print(f"\n{Fore.CYAN}Overall: {passed}/{len(results)} scenarios passed")
            sys.exit(0 if passed == len(results) else 1)

        elif args.orchestration:
            # Run orchestration with custom query
            success = await run_single_orchestration(args.orchestration, args.execution_mode, args.url)
            sys.exit(0 if success else 1)

        elif args.no_interactive:
            # Non-interactive mode without specific action
            print(f"{Fore.RED}Error: --no-interactive requires --demo, --demo-all, or --orchestration")
            sys.exit(1)

        else:
            # Interactive mode (default)
            await client.interactive_menu()

    except KeyboardInterrupt:
        print(f"\n{Fore.CYAN}👋 Interrupted by user. Goodbye!")
    except Exception as e:
        print(f"{Fore.RED}❌ Unexpected error: {e}")
        logger.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    print(f"{Fore.CYAN}� Financial Analysis Orchestration WebSocket Client")
    print(f"{Fore.CYAN}{'='*60}")
    print(f"{Fore.GREEN}Comprehensive client for testing orchestration functionality")
    print(f"{Fore.BLUE}Supports demo scenarios, enhanced orchestration, and interactive mode")
    print(f"{Fore.CYAN}{'='*60}\n")

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Fore.CYAN}👋 Goodbye!")
    except Exception as e:
        print(f"{Fore.RED}❌ Unexpected error: {e}")
        logger.error(f"Main execution error: {e}", exc_info=True)
